# Firebase Remote Config Integration for Emoji Gallery Categories

## Overview
This document outlines the implementation plan for integrating Firebase Remote Config with the emoji gallery fragment's category tabs. The implementation follows existing patterns from the Animation Fragment and maintains consistency with the app's MVI architecture.

## Current State Analysis

### Existing Animation Fragment Pattern
The Animation Fragment demonstrates the following remote config integration pattern:
- Uses `FirebaseRemoteConfigHelper.getString("animation_json")` to fetch JSON data
- Falls back to empty list when remote config fails or returns empty data
- Uses Gson for JSON parsing with data classes annotated with `@SerializedName`
- Implements comprehensive error handling with detailed logging
- Follows MVI pattern with proper state management

### Current Emoji Fragment Structure
- Uses hardcoded `BatteryStyleCategory.getMainFilterCategories()` for category tabs
- Implements MVI pattern with `BatteryGalleryViewModel` and `BatteryGalleryState`
- Uses `CategoryAdapter` for tab display with selection state management
- Has proper ViewBinding and Material 3 design integration

### Remote Config Default Data
The `remote_config_defaults.xml` contains emoji categories under key `"emoji_categories"`:
```json
[
  {"id":"hot_category","priority":1,"name":"🔥 HOT","status":true},
  {"id":"brainrot_category","priority":2,"name":"Brainrot","status":true,"is_new":true},
  {"id":"character_category","priority":3,"name":"Character ","status":true},
  {"id":"heart_category","priority":4,"name":"Heart","status":true},
  {"id":"cute_category","priority":5,"name":"Cute","status":true},
  {"id":"sticker3d_category","priority":6,"name":"Sticker 3D","status":true,"is_new":true},
  {"id":"emotion_category","priority":7,"name":"Emotion","status":true},
  {"id":"animal_category","priority":8,"name":"Animal","status":true},
  {"id":"food_category","priority":9,"name":"Food","status":true},
  {"id":"other_category","priority":10,"name":"Other","status":true}
]
```

## Implementation Plan

### Phase 1: Emoji Gallery Items Implementation

Based on analysis of the existing remote config structure, emoji items are already available in Firebase Remote Config under category-specific keys (e.g., `animal_category`, `brainrot_category`, `character_category`, etc.). Each category contains JSON arrays of emoji items with the following structure:

```json
{
  "id": "emoji-1",
  "category_id": "animal_category",
  "priority": 1,
  "name": "emoji battery_01",
  "thumbnail": "https://cdn-uploader.innovatex.one/thumnails/800/store/2025-01-07/1736237683_emojibattery_01.png",
  "photo": "https://cdn-uploader.innovatex.one/data/store/2025-01-07/1736237683_emojibattery_01.png",
  "status": true,
  "is_pro": true, // Optional field for premium items
  "custom_fields": {
    "battery": "https://cdn-uploader.innovatex.one/data/store/2025-01-07/1736237694_Battery_01.png",
    "emoji": "https://cdn-uploader.innovatex.one/data/store/2025-01-07/1736237701_Emoji_01.png"
  }
}
```

### Migration Strategy

1. **Current State**: The emoji gallery currently uses hardcoded `BatteryStyle` objects generated from `BatteryStyleCategory.getMainFilterCategories()` and fetched via `BatteryStyleRepository`.

2. **Target State**: Replace the current repository-based approach with direct Firebase Remote Config integration for emoji items, similar to how emoji categories are already implemented.

3. **Migration Approach**:
   - Create `EmojiItem` data model to match remote config structure
   - Create `EmojiItemService` to fetch items by category from remote config
   - Update `BatteryGalleryViewModel` to use emoji items instead of battery styles
   - Maintain backward compatibility during transition

### 1. Data Models

#### EmojiItem Data Class
Create a new data class to represent emoji items from Firebase Remote Config:

```kotlin
package com.tqhit.battery.one.features.emoji.domain.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing an emoji item from Firebase Remote Config.
 * Maps to the JSON structure defined in remote config for category-specific emoji items.
 */
data class EmojiItem(
    @SerializedName("id") val id: String,
    @SerializedName("category_id") val categoryId: String,
    @SerializedName("priority") val priority: Int,
    @SerializedName("name") val name: String,
    @SerializedName("thumbnail") val thumbnail: String,
    @SerializedName("photo") val photo: String,
    @SerializedName("status") val status: Boolean,
    @SerializedName("is_pro") val isPremium: Boolean = false,
    @SerializedName("custom_fields") val customFields: EmojiCustomFields
) {
    /**
     * Validates that the emoji item contains valid data
     */
    fun isValid(): Boolean {
        return id.isNotBlank() &&
               categoryId.isNotBlank() &&
               name.isNotBlank() &&
               thumbnail.isNotBlank() &&
               photo.isNotBlank() &&
               priority >= 0 &&
               customFields.isValid()
    }

    /**
     * Converts to BatteryStyle for compatibility with existing UI components
     */
    fun toBatteryStyle(): BatteryStyle {
        return BatteryStyle(
            id = id,
            name = name,
            category = BatteryStyleCategory.fromCategoryId(categoryId) ?: BatteryStyleCategory.CHARACTER,
            batteryImageUrl = customFields.battery,
            emojiImageUrl = customFields.emoji,
            isPremium = isPremium,
            isPopular = false, // Can be determined by category or other logic
            timestampEpochMillis = System.currentTimeMillis()
        )
    }
}

/**
 * Custom fields containing battery and emoji image URLs
 */
data class EmojiCustomFields(
    @SerializedName("battery") val battery: String,
    @SerializedName("emoji") val emoji: String
) {
    fun isValid(): Boolean {
        return battery.isNotBlank() && emoji.isNotBlank()
    }
}
```

#### EmojiCategory Data Class
Create a new data class to represent remote config emoji categories:

```kotlin
package com.tqhit.battery.one.features.emoji.domain.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing an emoji category from Firebase Remote Config.
 * Maps to the JSON structure defined in remote_config_defaults.xml
 */
data class EmojiCategory(
    @SerializedName("id") val id: String,
    @SerializedName("priority") val priority: Int,
    @SerializedName("name") val name: String,
    @SerializedName("status") val status: Boolean,
    @SerializedName("is_new") val isNew: Boolean = false
) {
    /**
     * Validates that the category contains valid data
     */
    fun isValid(): Boolean {
        return id.isNotBlank() && 
               name.isNotBlank() && 
               priority >= 0
    }
    
    /**
     * Maps to existing BatteryStyleCategory enum for compatibility
     */
    fun toBatteryStyleCategory(): BatteryStyleCategory? {
        return when (id.lowercase()) {
            "hot_category" -> BatteryStyleCategory.HOT
            "character_category" -> BatteryStyleCategory.CHARACTER
            "heart_category" -> BatteryStyleCategory.HEART
            "cute_category" -> BatteryStyleCategory.CUTE
            "animal_category" -> BatteryStyleCategory.ANIMAL
            "food_category" -> BatteryStyleCategory.FOOD
            else -> null // New categories not yet supported in enum
        }
    }
}
```

### 2. Remote Config Services

#### EmojiItemService
Create a dedicated service for fetching emoji items by category:

```kotlin
package com.tqhit.battery.one.features.emoji.data.service

import com.google.gson.Gson
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.features.emoji.domain.model.EmojiItem
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for fetching and parsing emoji items from Firebase Remote Config.
 * Fetches items by category ID and provides fallback to default items.
 */
@Singleton
class EmojiItemService @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val gson: Gson
) {
    companion object {
        private const val TAG = "EmojiItemService"
    }

    /**
     * Fetches emoji items for a specific category from Firebase Remote Config.
     * Returns only items with status=true, sorted by priority.
     */
    suspend fun getEmojiItemsByCategory(categoryId: String): List<EmojiItem> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Fetching emoji items for category: $categoryId")

            val jsonString = remoteConfigHelper.getString(categoryId)
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Retrieved JSON string length: ${jsonString.length} for category: $categoryId")

            if (jsonString.isBlank()) {
                BatteryLogger.w(TAG, "REMOTE_CONFIG: Empty JSON string for category: $categoryId, using fallback")
                return@withContext createFallbackItems(categoryId)
            }

            val items = parseEmojiItems(jsonString)
            val validItems = filterAndSortItems(items)

            BatteryLogger.d(TAG, "REMOTE_CONFIG: Successfully loaded ${validItems.size} valid items for category: $categoryId")
            return@withContext validItems

        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error fetching emoji items for category: $categoryId", e)
            return@withContext createFallbackItems(categoryId)
        }
    }

    private fun parseEmojiItems(jsonString: String): List<EmojiItem> {
        return try {
            val items = gson.fromJson(jsonString, Array<EmojiItem>::class.java).toList()
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Successfully parsed ${items.size} emoji items")
            items
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error parsing emoji items JSON", e)
            emptyList()
        }
    }

    private fun filterAndSortItems(items: List<EmojiItem>): List<EmojiItem> {
        val validItems = items.filter { item ->
            val isValid = item.isValid() && item.status
            if (!isValid) {
                BatteryLogger.w(TAG, "REMOTE_CONFIG: Filtering out invalid/disabled item: ${item.id}")
            }
            isValid
        }.sortedBy { it.priority }

        BatteryLogger.d(TAG, "REMOTE_CONFIG: Filtered to ${validItems.size} valid items")
        return validItems
    }

    private fun createFallbackItems(categoryId: String): List<EmojiItem> {
        BatteryLogger.d(TAG, "REMOTE_CONFIG: Creating fallback items for category: $categoryId")

        // Return empty list as fallback - UI should handle empty state gracefully
        return emptyList()
    }
}
```

#### EmojiCategoryService
Create a dedicated service following the AnimationDataService pattern:

```kotlin
package com.tqhit.battery.one.features.emoji.data.service

import com.google.gson.Gson
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.features.emoji.domain.model.EmojiCategory
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for fetching and parsing emoji category data from Firebase Remote Config.
 * Follows SOLID principles and existing app patterns.
 */
@Singleton
class EmojiCategoryService @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val gson: Gson
) {
    companion object {
        private const val TAG = "EmojiCategoryService"
        private const val EMOJI_CATEGORIES_KEY = "emoji_categories"
    }
    
    /**
     * Fetches emoji categories from Firebase Remote Config with fallback to defaults.
     * Returns only categories with status=true, sorted by priority.
     */
    suspend fun getEmojiCategories(): List<EmojiCategory> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Fetching emoji categories from remote config")
            
            val jsonString = remoteConfigHelper.getString(EMOJI_CATEGORIES_KEY)
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Retrieved JSON string length: ${jsonString.length}")
            
            if (jsonString.isBlank()) {
                BatteryLogger.w(TAG, "REMOTE_CONFIG: Empty JSON string, using fallback categories")
                return@withContext createFallbackCategories()
            }
            
            val categories = parseEmojiCategories(jsonString)
            val validCategories = filterAndSortCategories(categories)
            
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Successfully loaded ${validCategories.size} valid categories")
            return@withContext validCategories
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error fetching emoji categories", e)
            return@withContext createFallbackCategories()
        }
    }
    
    private fun parseEmojiCategories(jsonString: String): List<EmojiCategory> {
        return try {
            val categories = gson.fromJson(jsonString, Array<EmojiCategory>::class.java).toList()
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Successfully parsed ${categories.size} categories")
            categories
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error parsing emoji categories JSON", e)
            emptyList()
        }
    }
    
    private fun filterAndSortCategories(categories: List<EmojiCategory>): List<EmojiCategory> {
        val validCategories = categories.filter { category ->
            val isValid = category.isValid() && category.status
            if (!isValid) {
                BatteryLogger.w(TAG, "REMOTE_CONFIG: Filtering out invalid/disabled category: ${category.id}")
            }
            isValid
        }.sortedBy { it.priority }
        
        BatteryLogger.d(TAG, "REMOTE_CONFIG: Filtered to ${validCategories.size} valid categories")
        return validCategories
    }
    
    private fun createFallbackCategories(): List<EmojiCategory> {
        BatteryLogger.d(TAG, "REMOTE_CONFIG: Creating fallback categories")
        
        return listOf(
            EmojiCategory("hot_category", 1, "🔥 HOT", true, false),
            EmojiCategory("character_category", 3, "Character", true, false),
            EmojiCategory("heart_category", 4, "Heart", true, false),
            EmojiCategory("cute_category", 5, "Cute", true, false),
            EmojiCategory("animal_category", 8, "Animal", true, false),
            EmojiCategory("food_category", 9, "Food", true, false)
        )
    }
}
```

### 3. ViewModel Integration

#### Update BatteryGalleryViewModel
Modify the existing ViewModel to use emoji items from remote config:

```kotlin
// Add to BatteryGalleryViewModel class

@Inject
lateinit var emojiItemService: EmojiItemService

private val _currentEmojiItems = MutableStateFlow<List<EmojiItem>>(emptyList())
val currentEmojiItems: StateFlow<List<EmojiItem>> = _currentEmojiItems.asStateFlow()

/**
 * Loads emoji items for the selected category from remote config
 */
private fun loadEmojiItemsForCategory(categoryId: String) {
    viewModelScope.launch {
        try {
            Log.d(TAG, "REMOTE_CONFIG: Loading emoji items for category: $categoryId")
            val items = emojiItemService.getEmojiItemsByCategory(categoryId)
            _currentEmojiItems.value = items

            // Convert to BatteryStyle for compatibility with existing UI
            val batteryStyles = items.map { it.toBatteryStyle() }
            updateStateWithStyles(batteryStyles, false)

            Log.d(TAG, "REMOTE_CONFIG: Loaded ${items.size} emoji items for category: $categoryId")
        } catch (e: Exception) {
            Log.e(TAG, "REMOTE_CONFIG: Error loading emoji items for category: $categoryId", e)
            // Keep existing behavior as fallback
        }
    }
}

/**
 * Handles category selection by loading corresponding emoji items
 */
private fun selectCategory(category: BatteryStyleCategory) {
    Log.d(TAG, "Selecting category: ${category.displayName}")

    // Map BatteryStyleCategory to category ID for remote config
    val categoryId = category.toCategoryId()

    // Load emoji items for this category
    loadEmojiItemsForCategory(categoryId)

    // Update UI state
    val currentState = _uiState.value
    _uiState.value = currentState.copy(
        selectedCategory = category,
        searchQuery = "", // Clear search when changing category
        isSearchActive = false
    )
}
```

#### Extension Functions
Add extension functions to map between category types:

```kotlin
// Add to BatteryStyleCategory.kt or create CategoryExtensions.kt

/**
 * Maps BatteryStyleCategory to remote config category ID
 */
fun BatteryStyleCategory.toCategoryId(): String {
    return when (this) {
        BatteryStyleCategory.HOT -> "hot_category"
        BatteryStyleCategory.CHARACTER -> "character_category"
        BatteryStyleCategory.HEART -> "heart_category"
        BatteryStyleCategory.CUTE -> "cute_category"
        BatteryStyleCategory.ANIMAL -> "animal_category"
        BatteryStyleCategory.FOOD -> "food_category"
        else -> "character_category" // Default fallback
    }
}

/**
 * Maps remote config category ID to BatteryStyleCategory
 */
fun BatteryStyleCategory.Companion.fromCategoryId(categoryId: String): BatteryStyleCategory? {
    return when (categoryId.lowercase()) {
        "hot_category" -> BatteryStyleCategory.HOT
        "character_category" -> BatteryStyleCategory.CHARACTER
        "heart_category" -> BatteryStyleCategory.HEART
        "cute_category" -> BatteryStyleCategory.CUTE
        "animal_category" -> BatteryStyleCategory.ANIMAL
        "food_category" -> BatteryStyleCategory.FOOD
        "brainrot_category" -> null // New category not in enum yet
        "sticker3d_category" -> null // New category not in enum yet
        "emotion_category" -> null // New category not in enum yet
        "other_category" -> null // New category not in enum yet
        else -> null
    }
}
```

### 4. Fragment Updates

#### Update EmojiBatteryFragment
Modify the fragment to observe remote config categories:

```kotlin
// In EmojiBatteryFragment.setupUI()

// Replace hardcoded categories with remote config observation
private fun observeEmojiCategories() {
    viewLifecycleOwner.lifecycleScope.launch {
        viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
            viewModel.emojiCategories.collect { remoteCategories ->
                Log.d(TAG, "REMOTE_CONFIG: Received ${remoteCategories.size} categories from remote config")
                
                if (remoteCategories.isNotEmpty()) {
                    updateCategoryTabs(remoteCategories)
                } else {
                    // Keep existing hardcoded categories as fallback
                    Log.w(TAG, "REMOTE_CONFIG: No remote categories available, using hardcoded fallback")
                }
            }
        }
    }
}

private fun updateCategoryTabs(remoteCategories: List<EmojiCategory>) {
    // Update category adapter with remote config data
    // Handle "NEW" label display for categories with isNew=true
    // Maintain existing selection state and visual feedback
}
```

### 5. UI Enhancements

#### "NEW" Label Support
Update the category adapter to show "NEW" labels:

```kotlin
// In CategoryViewHolder.bind()

// Add NEW label handling
if (emojiCategory.isNew) {
    binding.newLabel.visibility = View.VISIBLE
} else {
    binding.newLabel.visibility = View.GONE
}
```

### 6. Testing Strategy

#### Unit Tests
- Test EmojiCategoryService with mocked FirebaseRemoteConfigHelper
- Test JSON parsing with valid and invalid data
- Test fallback mechanism when remote config fails
- Test category filtering and sorting logic

#### Integration Tests
- Test ViewModel integration with remote config service
- Test Fragment UI updates when categories change
- Test "NEW" label display functionality
- Test category selection with remote config data

#### Manual Testing
- Deploy app and verify categories load from remote config
- Test with empty remote config to verify fallback
- Test category selection and filtering
- Verify "NEW" labels appear correctly
- Test app behavior when remote config is unavailable

## Implementation Phases

### Phase 1: Core Data Models and Service
1. Create EmojiCategory data class
2. Implement EmojiCategoryService
3. Add unit tests for service layer

### Phase 2: ViewModel Integration
1. Update BatteryGalleryViewModel to use remote config
2. Add category loading state management
3. Test ViewModel integration

### Phase 3: UI Updates
1. Update EmojiBatteryFragment to observe remote categories
2. Implement "NEW" label support in CategoryAdapter
3. Test UI updates and visual feedback

### Phase 4: Testing and Validation
1. Comprehensive testing across all layers
2. Manual testing with various remote config scenarios
3. Performance validation and logging verification

## Logging Strategy

All remote config operations will include structured logging:
- `REMOTE_CONFIG:` prefix for easy filtering
- Success/failure states with detailed context
- Performance metrics for remote config fetch operations
- Category count and validation results

## Error Handling

Following existing app patterns:
- Graceful fallback to hardcoded categories when remote config fails
- Comprehensive exception handling with detailed logging
- Maintain app functionality even when remote config is unavailable
- User-friendly error states (though categories should always be available via fallback)
