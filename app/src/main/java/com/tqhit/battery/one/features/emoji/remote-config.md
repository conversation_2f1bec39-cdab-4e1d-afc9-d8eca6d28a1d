# Firebase Remote Config Integration for Emoji Gallery Categories

## Overview
This document outlines the implementation plan for integrating Firebase Remote Config with the emoji gallery fragment's category tabs. The implementation follows existing patterns from the Animation Fragment and maintains consistency with the app's MVI architecture.

## Current State Analysis

### Existing Animation Fragment Pattern
The Animation Fragment demonstrates the following remote config integration pattern:
- Uses `FirebaseRemoteConfigHelper.getString("animation_json")` to fetch JSON data
- Falls back to empty list when remote config fails or returns empty data
- Uses Gson for JSON parsing with data classes annotated with `@SerializedName`
- Implements comprehensive error handling with detailed logging
- Follows MVI pattern with proper state management

### Current Emoji Fragment Structure
- Uses hardcoded `BatteryStyleCategory.getMainFilterCategories()` for category tabs
- Implements MVI pattern with `BatteryGalleryViewModel` and `BatteryGalleryState`
- Uses `CategoryAdapter` for tab display with selection state management
- Has proper ViewBinding and Material 3 design integration

### Remote Config Default Data
The `remote_config_defaults.xml` contains emoji categories under key `"emoji_categories"`:
```json
[
  {"id":"hot_category","priority":1,"name":"🔥 HOT","status":true},
  {"id":"brainrot_category","priority":2,"name":"Brainrot","status":true,"is_new":true},
  {"id":"character_category","priority":3,"name":"Character ","status":true},
  {"id":"heart_category","priority":4,"name":"Heart","status":true},
  {"id":"cute_category","priority":5,"name":"Cute","status":true},
  {"id":"sticker3d_category","priority":6,"name":"Sticker 3D","status":true,"is_new":true},
  {"id":"emotion_category","priority":7,"name":"Emotion","status":true},
  {"id":"animal_category","priority":8,"name":"Animal","status":true},
  {"id":"food_category","priority":9,"name":"Food","status":true},
  {"id":"other_category","priority":10,"name":"Other","status":true}
]
```

## Implementation Plan

### 1. Data Models

#### EmojiCategory Data Class
Create a new data class to represent remote config emoji categories:

```kotlin
package com.tqhit.battery.one.features.emoji.domain.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing an emoji category from Firebase Remote Config.
 * Maps to the JSON structure defined in remote_config_defaults.xml
 */
data class EmojiCategory(
    @SerializedName("id") val id: String,
    @SerializedName("priority") val priority: Int,
    @SerializedName("name") val name: String,
    @SerializedName("status") val status: Boolean,
    @SerializedName("is_new") val isNew: Boolean = false
) {
    /**
     * Validates that the category contains valid data
     */
    fun isValid(): Boolean {
        return id.isNotBlank() && 
               name.isNotBlank() && 
               priority >= 0
    }
    
    /**
     * Maps to existing BatteryStyleCategory enum for compatibility
     */
    fun toBatteryStyleCategory(): BatteryStyleCategory? {
        return when (id.lowercase()) {
            "hot_category" -> BatteryStyleCategory.HOT
            "character_category" -> BatteryStyleCategory.CHARACTER
            "heart_category" -> BatteryStyleCategory.HEART
            "cute_category" -> BatteryStyleCategory.CUTE
            "animal_category" -> BatteryStyleCategory.ANIMAL
            "food_category" -> BatteryStyleCategory.FOOD
            else -> null // New categories not yet supported in enum
        }
    }
}
```

### 2. Remote Config Service

#### EmojiCategoryService
Create a dedicated service following the AnimationDataService pattern:

```kotlin
package com.tqhit.battery.one.features.emoji.data.service

import com.google.gson.Gson
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.features.emoji.domain.model.EmojiCategory
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for fetching and parsing emoji category data from Firebase Remote Config.
 * Follows SOLID principles and existing app patterns.
 */
@Singleton
class EmojiCategoryService @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val gson: Gson
) {
    companion object {
        private const val TAG = "EmojiCategoryService"
        private const val EMOJI_CATEGORIES_KEY = "emoji_categories"
    }
    
    /**
     * Fetches emoji categories from Firebase Remote Config with fallback to defaults.
     * Returns only categories with status=true, sorted by priority.
     */
    suspend fun getEmojiCategories(): List<EmojiCategory> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Fetching emoji categories from remote config")
            
            val jsonString = remoteConfigHelper.getString(EMOJI_CATEGORIES_KEY)
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Retrieved JSON string length: ${jsonString.length}")
            
            if (jsonString.isBlank()) {
                BatteryLogger.w(TAG, "REMOTE_CONFIG: Empty JSON string, using fallback categories")
                return@withContext createFallbackCategories()
            }
            
            val categories = parseEmojiCategories(jsonString)
            val validCategories = filterAndSortCategories(categories)
            
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Successfully loaded ${validCategories.size} valid categories")
            return@withContext validCategories
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error fetching emoji categories", e)
            return@withContext createFallbackCategories()
        }
    }
    
    private fun parseEmojiCategories(jsonString: String): List<EmojiCategory> {
        return try {
            val categories = gson.fromJson(jsonString, Array<EmojiCategory>::class.java).toList()
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Successfully parsed ${categories.size} categories")
            categories
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error parsing emoji categories JSON", e)
            emptyList()
        }
    }
    
    private fun filterAndSortCategories(categories: List<EmojiCategory>): List<EmojiCategory> {
        val validCategories = categories.filter { category ->
            val isValid = category.isValid() && category.status
            if (!isValid) {
                BatteryLogger.w(TAG, "REMOTE_CONFIG: Filtering out invalid/disabled category: ${category.id}")
            }
            isValid
        }.sortedBy { it.priority }
        
        BatteryLogger.d(TAG, "REMOTE_CONFIG: Filtered to ${validCategories.size} valid categories")
        return validCategories
    }
    
    private fun createFallbackCategories(): List<EmojiCategory> {
        BatteryLogger.d(TAG, "REMOTE_CONFIG: Creating fallback categories")
        
        return listOf(
            EmojiCategory("hot_category", 1, "🔥 HOT", true, false),
            EmojiCategory("character_category", 3, "Character", true, false),
            EmojiCategory("heart_category", 4, "Heart", true, false),
            EmojiCategory("cute_category", 5, "Cute", true, false),
            EmojiCategory("animal_category", 8, "Animal", true, false),
            EmojiCategory("food_category", 9, "Food", true, false)
        )
    }
}
```

### 3. ViewModel Integration

#### Update BatteryGalleryViewModel
Modify the existing ViewModel to use remote config categories:

```kotlin
// Add to BatteryGalleryViewModel class

@Inject
lateinit var emojiCategoryService: EmojiCategoryService

private val _emojiCategories = MutableStateFlow<List<EmojiCategory>>(emptyList())
val emojiCategories: StateFlow<List<EmojiCategory>> = _emojiCategories.asStateFlow()

/**
 * Loads emoji categories from remote config
 */
private fun loadEmojiCategories() {
    viewModelScope.launch {
        try {
            Log.d(TAG, "REMOTE_CONFIG: Loading emoji categories")
            val categories = emojiCategoryService.getEmojiCategories()
            _emojiCategories.value = categories
            Log.d(TAG, "REMOTE_CONFIG: Loaded ${categories.size} emoji categories")
        } catch (e: Exception) {
            Log.e(TAG, "REMOTE_CONFIG: Error loading emoji categories", e)
            // Keep existing hardcoded categories as ultimate fallback
        }
    }
}
```

### 4. Fragment Updates

#### Update EmojiBatteryFragment
Modify the fragment to observe remote config categories:

```kotlin
// In EmojiBatteryFragment.setupUI()

// Replace hardcoded categories with remote config observation
private fun observeEmojiCategories() {
    viewLifecycleOwner.lifecycleScope.launch {
        viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
            viewModel.emojiCategories.collect { remoteCategories ->
                Log.d(TAG, "REMOTE_CONFIG: Received ${remoteCategories.size} categories from remote config")
                
                if (remoteCategories.isNotEmpty()) {
                    updateCategoryTabs(remoteCategories)
                } else {
                    // Keep existing hardcoded categories as fallback
                    Log.w(TAG, "REMOTE_CONFIG: No remote categories available, using hardcoded fallback")
                }
            }
        }
    }
}

private fun updateCategoryTabs(remoteCategories: List<EmojiCategory>) {
    // Update category adapter with remote config data
    // Handle "NEW" label display for categories with isNew=true
    // Maintain existing selection state and visual feedback
}
```

### 5. UI Enhancements

#### "NEW" Label Support
Update the category adapter to show "NEW" labels:

```kotlin
// In CategoryViewHolder.bind()

// Add NEW label handling
if (emojiCategory.isNew) {
    binding.newLabel.visibility = View.VISIBLE
} else {
    binding.newLabel.visibility = View.GONE
}
```

### 6. Testing Strategy

#### Unit Tests
- Test EmojiCategoryService with mocked FirebaseRemoteConfigHelper
- Test JSON parsing with valid and invalid data
- Test fallback mechanism when remote config fails
- Test category filtering and sorting logic

#### Integration Tests
- Test ViewModel integration with remote config service
- Test Fragment UI updates when categories change
- Test "NEW" label display functionality
- Test category selection with remote config data

#### Manual Testing
- Deploy app and verify categories load from remote config
- Test with empty remote config to verify fallback
- Test category selection and filtering
- Verify "NEW" labels appear correctly
- Test app behavior when remote config is unavailable

## Implementation Phases

### Phase 1: Core Data Models and Service
1. Create EmojiCategory data class
2. Implement EmojiCategoryService
3. Add unit tests for service layer

### Phase 2: ViewModel Integration
1. Update BatteryGalleryViewModel to use remote config
2. Add category loading state management
3. Test ViewModel integration

### Phase 3: UI Updates
1. Update EmojiBatteryFragment to observe remote categories
2. Implement "NEW" label support in CategoryAdapter
3. Test UI updates and visual feedback

### Phase 4: Testing and Validation
1. Comprehensive testing across all layers
2. Manual testing with various remote config scenarios
3. Performance validation and logging verification

## Logging Strategy

All remote config operations will include structured logging:
- `REMOTE_CONFIG:` prefix for easy filtering
- Success/failure states with detailed context
- Performance metrics for remote config fetch operations
- Category count and validation results

## Error Handling

Following existing app patterns:
- Graceful fallback to hardcoded categories when remote config fails
- Comprehensive exception handling with detailed logging
- Maintain app functionality even when remote config is unavailable
- User-friendly error states (though categories should always be available via fallback)
