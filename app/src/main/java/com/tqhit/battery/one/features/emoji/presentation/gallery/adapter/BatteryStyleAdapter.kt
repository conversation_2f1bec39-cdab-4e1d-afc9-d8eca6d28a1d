package com.tqhit.battery.one.features.emoji.presentation.gallery.adapter

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ItemBatteryStyleBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.utils.BatteryLogger

/**
 * RecyclerView adapter for displaying battery styles in a grid layout.
 * Follows the established patterns from AnimationAdapter with Glide image loading.
 * 
 * This adapter:
 * - Uses ViewBinding for view access
 * - Implements Glide for image loading with proper error handling
 * - Shows premium indicators for premium styles
 * - Handles click events for style selection and premium unlock
 * - Uses shimmer loading effect while images load
 */
class BatteryStyleAdapter(
    private val parentContext: Context,
    private var items: List<BatteryStyle>,
    private val onStyleClick: (BatteryStyle) -> Unit,
    private val onPremiumUnlock: (BatteryStyle) -> Unit
) : RecyclerView.Adapter<BatteryStyleViewHolder>() {
    
    companion object {
        private const val TAG = "BatteryStyleAdapter"
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BatteryStyleViewHolder {
        Log.d(TAG, "CLICK_DEBUG: Creating new ViewHolder")
        val binding = ItemBatteryStyleBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return BatteryStyleViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: BatteryStyleViewHolder, position: Int) {
        Log.d(TAG, "CLICK_DEBUG: Binding item at position $position: ${items[position].name}")
        holder.bind(
            items[position],
            parentContext,
            onStyleClick,
            onPremiumUnlock,
            position
        )
    }
    
    override fun getItemCount(): Int = items.size
    
    /**
     * Updates the adapter with new battery styles
     */
    fun updateItems(newItems: List<BatteryStyle>) {
        this.items = newItems
        notifyDataSetChanged()
    }
}

/**
 * ViewHolder for battery style items
 */
class BatteryStyleViewHolder(private val binding: ItemBatteryStyleBinding) : ViewHolder(binding.root) {
    
    companion object {
        private const val TAG = "BatteryStyleViewHolder"
    }
    
    fun bind(
        item: BatteryStyle,
        parentContext: Context,
        onStyleClick: (BatteryStyle) -> Unit,
        onPremiumUnlock: (BatteryStyle) -> Unit,
        position: Int
    ) {
        try {
            // Log layout parameters for debugging
            val layoutParams = itemView.layoutParams
            Log.d(TAG, "CLICK_DEBUG: Binding position $position - ${item.name} - Layout params: width=${layoutParams?.width}, height=${layoutParams?.height}")

            // Start shimmer loading effect
            binding.shimmerLayout.startShimmer()
            binding.shimmerLayout.visibility = View.VISIBLE

            // Set style name
            binding.styleName.text = item.name

            // Show/hide premium indicator
            binding.lockBtn.visibility = if (item.isPremium) View.VISIBLE else View.GONE

            // Load style preview image
            loadStylePreview(item)

            // Set click listeners
            setupClickListeners(item, onStyleClick, onPremiumUnlock, position)

            Log.d(TAG, "CLICK_DEBUG: Bound battery style at position $position: ${item.name}, premium: ${item.isPremium}")
        } catch (exception: Exception) {
            Log.e(TAG, "Error binding battery style: ${item.name}", exception)
            stopShimmerAndShowError()
        }
    }
    
    /**
     * Loads the style preview image using Glide
     */
    private fun loadStylePreview(item: BatteryStyle) {
        // Use the battery image as the main preview
        val imageUrl = item.batteryImageUrl.ifBlank { item.emojiImageUrl }
        
        if (imageUrl.isBlank()) {
            Log.w(TAG, "No image URL available for style: ${item.name}")
            stopShimmerAndShowError()
            return
        }
        
        Glide.with(itemView)
            .load(imageUrl)
            .centerCrop()
            .placeholder(R.drawable.placeholder_battery_style)
            .error(R.drawable.error_battery_style)
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>,
                    isFirstResource: Boolean
                ): Boolean {
                    Log.w(TAG, "Image load failed for style: ${item.name}, URL: $imageUrl", e)
                    stopShimmerAndShowError()
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>,
                    dataSource: com.bumptech.glide.load.DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    Log.d(TAG, "Image loaded successfully for style: ${item.name}")
                    stopShimmer()
                    return false
                }
            })
            .into(binding.styleImage)
    }
    
    /**
     * Sets up click listeners for the item
     */
    private fun setupClickListeners(
        item: BatteryStyle,
        onStyleClick: (BatteryStyle) -> Unit,
        onPremiumUnlock: (BatteryStyle) -> Unit,
        position: Int
    ) {
        // Main item click
        binding.root.setOnClickListener {
            Log.d(TAG, "CLICK_DEBUG: Position $position - Style clicked: ${item.name}")
            onStyleClick(item)
        }

        // Premium unlock button click
        if (item.isPremium) {
            binding.lockBtn.setOnClickListener {
                Log.d(TAG, "CLICK_DEBUG: Position $position - Premium unlock clicked for style: ${item.name}")
                onPremiumUnlock(item)
            }
        }

        // Style image click (same as main click)
        binding.styleImage.setOnClickListener {
            Log.d(TAG, "CLICK_DEBUG: Position $position - Style image clicked: ${item.name}")
            onStyleClick(item)
        }
    }
    
    /**
     * Stops shimmer loading effect
     */
    private fun stopShimmer() {
        try {
            binding.shimmerLayout.stopShimmer()
            binding.shimmerLayout.visibility = View.GONE
        } catch (exception: Exception) {
            Log.e(TAG, "Error stopping shimmer", exception)
        }
    }
    
    /**
     * Stops shimmer and shows error state
     */
    private fun stopShimmerAndShowError() {
        stopShimmer()
        // The error drawable will be shown by Glide
    }
}
